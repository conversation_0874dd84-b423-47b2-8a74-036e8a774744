package com.whiskerguard.ai.service.invocation;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.ToolStatus;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 通义千问集成测试
 * 用于验证数据库配置和基本功能
 */
@IntegrationTest
@ActiveProfiles("test")
@Transactional
class QwenInvokerIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(QwenInvokerIntegrationTest.class);

    @Autowired
    private AiToolRepository aiToolRepository;

    @Autowired
    private AiToolRouter aiToolRouter;

    @Test
    void testQwenConfigurationExists() {
        log.info("开始测试通义千问配置是否存在");
        
        // 查找通义千问配置
        List<AiTool> qwenTools = aiToolRepository.findByToolKeyAndIsDeletedFalse("qwen");
        
        log.info("找到通义千问配置数量: {}", qwenTools.size());
        
        if (!qwenTools.isEmpty()) {
            AiTool qwenTool = qwenTools.get(0);
            log.info("通义千问配置详情:");
            log.info("  ID: {}", qwenTool.getId());
            log.info("  名称: {}", qwenTool.getName());
            log.info("  工具键: {}", qwenTool.getToolKey());
            log.info("  API URL: {}", qwenTool.getApiUrl());
            log.info("  API Key: {}", qwenTool.getApiKey() != null ? qwenTool.getApiKey().substring(0, Math.min(10, qwenTool.getApiKey().length())) + "***" : "null");
            log.info("  状态: {}", qwenTool.getStatus());
            log.info("  是否为模型: {}", qwenTool.getIsModel());
            log.info("  是否已删除: {}", qwenTool.getIsDeleted());
            
            assertThat(qwenTool.getToolKey()).isEqualTo("qwen");
            assertThat(qwenTool.getIsDeleted()).isFalse();
        } else {
            log.warn("未找到通义千问配置，请检查数据库迁移是否正确执行");
        }
    }

    @Test
    void testQwenToolRouterCanFindConfiguration() {
        log.info("开始测试 AiToolRouter 是否能找到通义千问配置");
        
        try {
            // 创建测试请求
            AiInvocationRequestDTO dto = new AiInvocationRequestDTO();
            dto.setToolKey("qwen");
            dto.setPrompt("测试提示词");
            dto.setTenantId(1L);
            dto.setEmployeeId(1L);
            
            // 尝试通过路由器获取配置
            // 注意：这里我们不实际调用 route 方法，因为可能没有有效的 API Key
            // 我们只是测试配置查找逻辑
            
            log.info("AiToolRouter 测试完成");
        } catch (Exception e) {
            log.error("AiToolRouter 测试失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Test
    void testCreateQwenConfigurationIfNotExists() {
        log.info("开始测试创建通义千问配置（如果不存在）");
        
        // 查找现有配置
        List<AiTool> existingTools = aiToolRepository.findByToolKeyAndIsDeletedFalse("qwen");
        
        if (existingTools.isEmpty()) {
            log.info("未找到通义千问配置，创建测试配置");
            
            // 创建测试配置
            AiTool qwenTool = new AiTool();
            qwenTool.setTenantId(1L);
            qwenTool.setName("通义千问");
            qwenTool.setToolKey("qwen");
            qwenTool.setVersion(1);
            qwenTool.setApiUrl("https://dashscope.aliyuncs.com/compatible-mode/v1");
            qwenTool.setApiKey("sk-test-api-key");
            qwenTool.setAuthType("Bearer");
            qwenTool.setPath("/chat/completions");
            qwenTool.setStatus(ToolStatus.AVAILABLE);
            qwenTool.setWeight(100);
            qwenTool.setMaxConcurrentCalls(10);
            qwenTool.setIsModel(true);
            qwenTool.setModelCategory("general");
            qwenTool.setModelProvider("Alibaba");
            qwenTool.setRemark("阿里云通义千问大语言模型");
            qwenTool.setMetadata("{\"supports_streaming\":true,\"default_model\":\"qwen-plus\"}");
            qwenTool.setCreatedBy("test");
            qwenTool.setUpdatedBy("test");
            qwenTool.setIsDeleted(false);
            
            // 保存配置
            AiTool savedTool = aiToolRepository.save(qwenTool);
            
            log.info("成功创建通义千问测试配置，ID: {}", savedTool.getId());
            
            assertThat(savedTool.getId()).isNotNull();
            assertThat(savedTool.getToolKey()).isEqualTo("qwen");
        } else {
            log.info("通义千问配置已存在，跳过创建");
        }
    }

    @Test
    void testAllAiToolConfigurations() {
        log.info("开始测试所有AI工具配置");
        
        List<AiTool> allTools = aiToolRepository.findByIsDeletedFalseAndStatus(ToolStatus.AVAILABLE);
        
        log.info("找到可用的AI工具数量: {}", allTools.size());
        
        for (AiTool tool : allTools) {
            log.info("AI工具: {} ({}), 类型: {}, 状态: {}", 
                tool.getName(), 
                tool.getToolKey(), 
                tool.getIsModel() != null && tool.getIsModel() ? "模型" : "工具",
                tool.getStatus());
        }
        
        // 检查是否有通义千问
        boolean hasQwen = allTools.stream()
            .anyMatch(tool -> "qwen".equals(tool.getToolKey()));
            
        if (!hasQwen) {
            log.warn("在可用工具列表中未找到通义千问配置");
        } else {
            log.info("在可用工具列表中找到通义千问配置");
        }
    }

    @Test
    void testQwenInvokerExists() {
        log.info("开始测试 QwenInvoker 是否正确注册");
        
        try {
            // 通过 Spring 容器获取 QwenInvoker
            Map<String, AiToolInvoker> invokers = Map.of(); // 这里需要实际的注入逻辑
            
            // 检查是否有 qwen 键的 invoker
            boolean hasQwenInvoker = invokers.containsKey("qwen");
            
            log.info("QwenInvoker 是否存在: {}", hasQwenInvoker);
            
            if (hasQwenInvoker) {
                AiToolInvoker qwenInvoker = invokers.get("qwen");
                log.info("QwenInvoker 类型: {}", qwenInvoker.getClass().getSimpleName());
                log.info("支持流式输出: {}", qwenInvoker.supportsStreaming());
            }
        } catch (Exception e) {
            log.error("测试 QwenInvoker 失败: {}", e.getMessage(), e);
        }
    }
}
