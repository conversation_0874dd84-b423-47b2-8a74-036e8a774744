# ===================================================================
# Spring Boot configuration for the "prod" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: INFO
    tech.jhipster: INFO
    com.whiskerguard.ai: INFO

management:
  prometheus:
    metrics:
      export:
        enabled: false
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  cloud:
    consul:
      discovery:
        prefer-ip-address: true
        ip-address: ************
      host: dev.consule.mbbhg.com
      port: 8500
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **********************************************************************************************************************************************************************************************
    username: root
    password: MBB_root_2025@
    hikari:
      poolName: Hikari
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: prod
    enabled: false
  task:
    execution:
      thread-name-prefix: whiskerguard-ai-service-task-
      pool:
        core-size: 8 # 生产环境核心线程数
        max-size: 50 # 生产环境最大线程数
        queue-capacity: 2000 # 生产环境队列容量
        keep-alive: 60s # 线程空闲时间
        allow-core-thread-timeout: true # 允许核心线程超时
    scheduling:
      thread-name-prefix: whiskerguard-ai-service-scheduling-
      pool:
        size: 4 # 生产环境调度线程数
  thymeleaf:
    cache: true

# ===================================================================
# To enable TLS in production, generate a certificate using:
# keytool -genkey -alias whiskerguardaiservice -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# See details in topic "Create a Java Keystore (.JKS) from Let's Encrypt Certificates" on https://maximilian-boehm.com/en-gb/blog
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#   port: 443
#   ssl:
#     key-store: classpath:config/tls/keystore.p12
#     key-store-password: password
#     key-store-type: PKCS12
#     key-alias: selfsigned
#     # The ciphers suite enforce the security by deactivating some old and deprecated SSL cipher, this list was tested against SSL Labs (https://www.ssllabs.com/ssltest/)
#     ciphers: TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 ,TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 ,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,TLS_DHE_RSA_WITH_AES_128_CBC_SHA,TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,TLS_DHE_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,TLS_RSA_WITH_CAMELLIA_128_CBC_SHA
# ===================================================================
server:
  port: 8085
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  cache: # Cache configuration
    redis: # Redis configuration
      expiration: 3600 # By default objects stay 1 hour (in seconds) in the cache
      server: redis://dev.redis.mbbhg.com:6379
      cluster: false
      # server: redis://localhost:6379,redis://localhost:16379,redis://localhost:26379
      # cluster: true
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        # As this is the PRODUCTION configuration, you MUST change the default key, and store it securely:
        # - In the Consul configserver
        # - In a separate `application-prod.yml` file, in the same folder as your executable JAR file
        # - In the `JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET` environment variable
        base64-secret: YzU3OGViYWI3NzBjNzAxMTM1MDNhNzczZTZmZTJiNzk1Y2ZkMzUxNWM0ODIyMzA5NmU3MjA0M2RjNjUwYTAxYTRhYzFkMzg4YTFmOTVjYzJlZTY0YWY3ZjEzNjQ0NWQ3ODU4NzczZTY1MzY0MjQzM2NiNDc2NDMzNWRiNDk4NWE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
# Temporal Configuration
temporal:
  server:
    host: ************
    port: 7233
  task-queue: AiTaskQueue

# RAG Configuration for Production
whiskerguard:
  ai:
    rag:
      enabled: false # 生产环境启用 RAG
      default-tenant-id: 1
      top-k: 3
      distance-metric: cosine
      min-score: 0.6
      fast-fail: true # 启用快速失败模式，避免用户等待
    claude: # Claude AI 配置
      api-url: https://api.anthropic.com
      api-key: ************************************************************************************************************
      model: claude-3-opus-20240229
      path: /v1/messages
      max-tokens: 4000
      temperature: 0.7

# Feign 客户端配置优化
feign:
  circuitbreaker:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 30000
      whiskerguardretrievalservice:
        connectTimeout: 3000 # 连接超时 3 秒 - 减少连接等待时间
        readTimeout: 8000 # 读取超时 8 秒 - 大幅减少读取超时，避免用户感觉卡死
        loggerLevel: basic

# Resilience4j 熔断器配置 - 针对 RAG 服务和 AI 服务优化
resilience4j:
  circuitbreaker:
    instances:
      rag-service:
        registerHealthIndicator: true
        slidingWindowSize: 10 # 滑动窗口大小
        minimumNumberOfCalls: 5 # 最小调用次数
        permittedNumberOfCallsInHalfOpenState: 3 # 半开状态允许的调用次数
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 30s # 熔断器打开状态持续时间
        failureRateThreshold: 50 # 失败率阈值 50%
        eventConsumerBufferSize: 10
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - feign.FeignException.ServiceUnavailable
          - feign.FeignException.InternalServerError
          - feign.RetryableException
          - java.util.concurrent.TimeoutException
      # 通义千问 API 熔断器配置
      qwen:
        registerHealthIndicator: true
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 60s
        failureRateThreshold: 50
        eventConsumerBufferSize: 20
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - org.springframework.web.reactive.function.client.WebClientResponseException
          - java.util.concurrent.TimeoutException
      # 通义千问流式调用熔断器配置
      qwen-stream:
        registerHealthIndicator: true
        slidingWindowSize: 20
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 60s
        failureRateThreshold: 50
        eventConsumerBufferSize: 20
        recordExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - org.springframework.web.reactive.function.client.WebClientResponseException
          - java.util.concurrent.TimeoutException
  retry:
    instances:
      rag-service:
        maxAttempts: 2 # 最多重试 2 次（总共 3 次调用）
        waitDuration: 500ms # 重试间隔 500ms
        retryExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - feign.RetryableException
          - java.util.concurrent.TimeoutException
      # 通义千问 API 重试配置 - 生产环境
      qwen:
        maxAttempts: 3
        waitDuration: 2000ms
        retryExceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
          - org.springframework.web.reactive.function.client.WebClientRequestException
          - java.util.concurrent.TimeoutException
  # 通义千问 API 限流配置 - 生产环境
  bulkhead:
    instances:
      qwen:
        maxConcurrentCalls: 20
        maxWaitDuration: 10000ms
      qwen-stream:
        maxConcurrentCalls: 10
        maxWaitDuration: 5000ms
